# Interactive Portfolio - <PERSON>

[![Website Status](https://img.shields.io/badge/Status-Live-brightgreen.svg)](https://muhammad-trinanda.fun/)

Welcome! I'm <PERSON>, a final-year Sharia Accounting student with skills in graphic design, business data analysis, and web development. This interactive portfolio showcases my projects and abilities.

## 🚀 Demo

*   **Main Page:** [https://muhammad-trinanda.fun/](https://muhammad-trinanda.fun/)
*   **Marketplace:** [https://muhammad-trinanda.fun/marketplace/](https://muhammad-trinanda.fun/marketplace/)

## ✨ Key Features

*   **Responsive Design:**  Optimized for desktops, tablets, and mobile phones.
*   **Interactive Animations:**
    *   **Loading Animation:**  A custom loading animation using the ldBar library from [loading.io](https://loading.io/) greets visitors.
    *   **Scroll Animations:**  Smooth animations reveal elements as you scroll down the page.
    *   **Hover Effects:** Interactive elements respond to mouse hover.
    *   **Animated Cartoon:** A fun, animated cartoon character walks across the screen during page load.
*   **Digital Products Marketplace:**  Offers a selection of digital products and services (**Coming Soon**).

## 🛠️ Technologies Used

*   **HTML5:**  Website structure.
*   **CSS3:**  Styling and animations.
*   **JavaScript (ES6+):**  Interactivity, smooth scrolling, animations, and marketplace logic.  Uses the `DOMContentLoaded` event listener for optimal performance.
*    **loading-bar.min.js and loading-bar.min.css** costum loading animation
*   **ldBar (from loading.io):**  For the custom loading animation.
*   **Font Awesome:**  Icons.
*   **GitHub Pages:**  Website hosting.

## 📁 Project Structure
```
My-Interactive-Website/
├── docs/                    <-- Main folder for GitHub Pages (contains the built website)
│   ├── index.html           <-- Main portfolio page
│   ├── style.css            <-- Main page CSS
│   ├── script.js           <-- Main page JavaScript
│   ├── marketplace/
│   │   ├── index.html       <-- Marketplace page
│   │   ├── style.css        <-- Marketplace CSS
│   │   ├── script.js        <-- Marketplace JavaScript
│   │   ├── products.js      <-- Product data
│   │   └── images/          <-- Marketplace product images
│   │       ├── financial-statement.jpg
│   │       ├── graphic-design.jpg
│   │       ├── data-analysis.jpg
│   │       └── website-development.jpg
│   ├── coming-soon/
│   │   ├── index.html       <-- "Coming Soon" page
│   │   ├── style.css        <-- "Coming Soon" CSS
│   │   └── script.js        <-- "Coming Soon" JavaScript
│   ├── images/              <-- General image folder
│   │   ├── My Profile.png
│   │   ├── Nanda_Gif_With_Reverse_Compressed.gif
│   │   └── ...
│   ├── loading              <-- loading animation library
│   │    ├── loading-bar.min.js
│   │    └── loading-bar.min.css
│   └── favicon.ico          <-- Website icon
└── README.md                <-- This file
```
## 🧑‍💻 About Me

I'm a highly motivated final-year Sharia Accounting student at UINSU with a passion for combining financial knowledge with creative and technical skills.  I'm proficient in:

*   Accounting (cycles, financial statements, analysis, MYOB, Accurate).
*   Graphic Design (logos, brochures, posters, social media content; Adobe Photoshop, Illustrator).
*   Business Data Analysis (Excel, SPSS, Tableau, Power BI).
*   Microsoft Office Specialist
*   Web Development (HTML, CSS, JavaScript).

I'm a quick learner, adaptable, and enjoy taking on new challenges.  I'm seeking opportunities where I can contribute my diverse skills and dedication.

## 🛒 Marketplace

The marketplace will feature a selection of my digital products and services (**Coming Soon**):

*   Financial Statement Creation Service
*   Graphic Design Service
*   Business Data Analysis Service
*   Website Development Service

## 📞 Contact

Interested in collaborating or just want to chat?  Contact me:

*   **Email:** [<EMAIL>](mailto:<EMAIL>)
*   **LinkedIn:** [https://www.linkedin.com/in/muhammad-trinanda/](https://www.linkedin.com/in/muhammad-trinanda/)
*   **Instagram:** [https://www.instagram.com/trinanda321](https://www.instagram.com/trinanda321)

## 🤝 Contributing

This project is currently closed-source.  However, if you find any bugs or have suggestions, please create an issue in this repository.

## 📝 License

This project is closed-source.  You may view the code for educational purposes, but distribution, modification, or commercial use is strictly prohibited without my explicit written permission.
